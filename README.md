# Sistema de Registro Universitario

## 📋 Descripción

Este proyecto implementa un sistema completo de registro universitario desarrollado con Spring Boot. El sistema permite la gestión de estudiantes, materias, docentes e inscripciones, con un enfoque en la seguridad, rendimiento y usabilidad.

## 🚀 Características Principales

- **Autenticación y Autorización**: Sistema de login basado en JWT con roles (Administrador, Docente, Estudiante)
- **Gestión de Estudiantes**: CRUD completo con validaciones
- **Gestión de Materias**: CRUD completo con asignación de docentes
- **Sistema de Inscripciones**: Permite a estudiantes inscribirse en materias con validaciones de prerrequisitos
- **Evaluaciones**: Registro de calificaciones y estados de inscripción
- **Caché con Redis**: Optimización de rendimiento para consultas frecuentes
- **Documentación API**: Interfaz <PERSON>gger/OpenAPI para explorar y probar la API
- **Validaciones Robustas**: Validación en múltiples niveles (entidad, servicio, controlador)
- **Manejo de Excepciones**: Respuestas de error personalizadas y consistentes

## 🛠️ Tecnologías Utilizadas

- **Backend**: Spring Boot, Spring Security, Spring Data JPA
- **Base de Datos**: PostgreSQL
- **Caché**: Redis
- **Seguridad**: JWT (JSON Web Tokens)
- **Documentación**: Swagger/OpenAPI
- **Frontend**: Deno/Fresh y Preact (en desarrollo)

## 📖 Documentación

El proyecto incluye documentación detallada para desarrolladores y usuarios:

- **[Manual Técnico](docs/Manual_Tecnico.md)**: Documentación técnica detallada sobre la arquitectura, configuración y componentes del sistema.
- **[Guía de Pruebas con Postman](docs/Pruebas_Postman.md)**: Ejemplos detallados para probar cada endpoint de la API.
- **[Documentación de API en Español](docs/API_Documentation_ES.md)**: Referencia completa de la API para desarrolladores frontend.

## 🏗️ Estructura del Proyecto

```
src/main/java/
├── controller/     # Controladores REST
├── service/        # Lógica de negocio
├── model/          # Entidades y modelos
├── repository/     # Acceso a datos
├── dto/            # Objetos de transferencia de datos
├── validation/     # Validadores personalizados
├── config/         # Configuraciones
└── security/       # Configuración de seguridad y JWT
```

## 🚀 Instalación y Ejecución

### Requisitos Previos

- Java 17 o superior
- PostgreSQL 12 o superior
- Redis 6 o superior
- Maven 3.8 o superior

### Pasos de Instalación

1. Clona el repositorio:
```bash
git clone https://github.com/tu-usuario/registro-universitario.git
```

2. Configura la base de datos en `application.properties`:
```properties
spring.datasource.url=********************************************
spring.datasource.username=tu_usuario
spring.datasource.password=tu_contraseña
```

3. Configura Redis en `application.properties`:
```properties
spring.redis.host=localhost
spring.redis.port=6379
```

4. Compila y ejecuta el proyecto:
```bash
mvn spring-boot:run
```

5. Accede a la API en `http://localhost:8080`

6. Explora la documentación Swagger en `http://localhost:8080/swagger-ui.html`

## 🔒 Seguridad

El sistema implementa seguridad basada en roles:

- **ADMIN**: Acceso completo a todas las funcionalidades
- **DOCENTE**: Gestión de materias asignadas y evaluaciones
- **ESTUDIANTE**: Inscripción a materias y consulta de información

La autenticación se realiza mediante JWT (JSON Web Tokens) que deben incluirse en el encabezado `Authorization` de las solicitudes.

## 🌐 Desarrollo Frontend

Se está desarrollando un frontend utilizando Deno/Fresh y Preact que consumirá esta API. La documentación para desarrolladores frontend está disponible en [API_Documentation_ES.md](docs/API_Documentation_ES.md).
