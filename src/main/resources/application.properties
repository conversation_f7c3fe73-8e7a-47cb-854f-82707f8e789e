# Nombre de la aplicacion Spring Boot
spring.application.name=mi-proyecto-spring-boot

# Puerto en el que se ejecutara el servidor
server.port=8080

# Configuracion de la base de datos PostgreSQL
# URL de conexion a la base de datos PostgreSQL (protocolo, host, puerto y nombre de la base de datos)
spring.datasource.url=********************************************
# Nombre de usuario para conectarse a la base de datos
spring.datasource.username=postgres
# Contrasena para conectarse a la base de datos
spring.datasource.password=123456
# Clase del driver JDBC para PostgreSQL
spring.datasource.driver-class-name=org.postgresql.Driver

# Configuracion de JPA e Hibernate
# Estrategia para la creacion y actualizacion de las tablas en la base de datos
spring.jpa.hibernate.ddl-auto=update
# Muestra las consultas SQL generadas por Hibernate en la consola
spring.jpa.show-sql=true
# Dialecto de Hibernate para PostgreSQL, que optimiza las consultas para esta base de datos
spring.jpa.properties.hibernate.dialect=org.hibernate.dialect.PostgreSQLDialect

spring.mvc.throw-exception-if-no-handler-found=true
#
spring.web.resources.add-mappings=true
#

# Agregamos la configuracion para la conexion con redis
spring.cache.type=redis
spring.redis.host=localhost
spring.redis.port=6379

# Configuracion de Spring Session
spring.session.store-type=jdbc
spring.session.jdbc.initialize-schema=always
spring.session.timeout=30m


# Configuracion JWT
# Clave secreta utilizada para firmar los tokens JWT
# Esta clave debe ser lo suficientemente larga y compleja para garantizar la seguridad
# del token. En un entorno de produccion, se recomienda utilizar una clave generada aleatoriamente y almacenarla de forma segura.
# En este caso, se utiliza una clave de ejemplo, pero en un entorno real, se debe cambiar por una clave segura.
app.jwtSecret=QWERTYUIOPASDFGHJKLZXCVBNMQWERTYUIOPASDFGHJKLZXCVBNMQWERTYUIOPASDFGHJKLZXCVBNM
#1 dia en milisegundos
app.jwtExpirationMs=86400000

# Configuracion de Swagger/OpenAPI
springdoc.api-docs.path=/api-docs
springdoc.swagger-ui.path=/swagger-ui.html
springdoc.swagger-ui.operationsSorter=method
springdoc.swagger-ui.tagsSorter=alpha
springdoc.swagger-ui.filter=true

