-- Script para corregir las secuencias de generación de IDs en PostgreSQL

-- 1. Verificar las secuencias existentes
SELECT c.relname AS sequence_name
FROM pg_class c
WHERE c.relkind = 'S';

-- 2. Verificar el valor actual de la secuencia persona_id_persona_seq
SELECT last_value, is_called FROM persona_id_persona_seq;

-- 3. Obtener el valor máximo actual de id_persona
SELECT MAX(id_persona) FROM persona;

-- 4. Reiniciar la secuencia al valor máximo + 1
SELECT setval('persona_id_persona_seq', (SELECT MAX(id_persona) FROM persona) + 1, true);

-- 5. Verificar el nuevo valor de la secuencia
SELECT last_value, is_called FROM persona_id_persona_seq;
