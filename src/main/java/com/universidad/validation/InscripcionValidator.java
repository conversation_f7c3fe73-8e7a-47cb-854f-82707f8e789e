package com.universidad.validation;

import org.springframework.stereotype.Component;
import org.springframework.beans.factory.annotation.Autowired;

import com.universidad.dto.InscripcionDTO;
import com.universidad.repository.InscripcionRepository;
import com.universidad.repository.EstudianteRepository;
import com.universidad.repository.MateriaRepository;
import com.universidad.model.Estudiante;
import com.universidad.model.Materia;
import com.universidad.model.Inscripcion;

import java.util.Arrays;
import java.util.List;
import java.util.Optional;

@Component
public class InscripcionValidator {

    @Autowired
    private InscripcionRepository inscripcionRepository;
    
    @Autowired
    private EstudianteRepository estudianteRepository;
    
    @Autowired
    private MateriaRepository materiaRepository;

    public void validarEstudianteExiste(Long estudianteId) {
        if (!estudianteRepository.existsById(estudianteId)) {
            throw new IllegalArgumentException("El estudiante con ID " + estudianteId + " no existe");
        }
    }

    public void validarMateriaExiste(Long materiaId) {
        if (!materiaRepository.existsById(materiaId)) {
            throw new IllegalArgumentException("La materia con ID " + materiaId + " no existe");
        }
    }

    public void validarInscripcionNoExiste(Long estudianteId, Long materiaId) {
        Estudiante estudiante = estudianteRepository.findById(estudianteId)
                .orElseThrow(() -> new IllegalArgumentException("Estudiante no encontrado"));
                
        Materia materia = materiaRepository.findById(materiaId)
                .orElseThrow(() -> new IllegalArgumentException("Materia no encontrada"));
                
        Optional<Inscripcion> inscripcionExistente = inscripcionRepository.findByEstudianteAndMateria(estudiante, materia);
        
        if (inscripcionExistente.isPresent()) {
            throw new IllegalArgumentException("El estudiante ya está inscrito en esta materia");
        }
    }

    public void validarEstadoInscripcion(String estado) {
        List<String> estadosValidos = Arrays.asList("PENDIENTE", "APROBADA", "RECHAZADA");
        
        if (!estadosValidos.contains(estado)) {
            throw new IllegalArgumentException("El estado de inscripción no es válido. Estados válidos: " + estadosValidos);
        }
    }

    public void validarCalificacion(Double calificacion) {
        if (calificacion != null && (calificacion < 0 || calificacion > 10)) {
            throw new IllegalArgumentException("La calificación debe estar entre 0 y 10");
        }
    }

    public void validacionCompletaInscripcion(InscripcionDTO inscripcion) {
        validarEstudianteExiste(inscripcion.getEstudianteId());
        validarMateriaExiste(inscripcion.getMateriaId());
        validarInscripcionNoExiste(inscripcion.getEstudianteId(), inscripcion.getMateriaId());
        
        if (inscripcion.getEstado() != null) {
            validarEstadoInscripcion(inscripcion.getEstado());
        }
        
        if (inscripcion.getCalificacion() != null) {
            validarCalificacion(inscripcion.getCalificacion());
        }
    }
}
