package com.universidad.dto;

import jakarta.validation.constraints.*;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import java.io.Serializable;
import java.time.LocalDateTime;

@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class InscripcionDTO implements Serializable {

    private Long id;

    @NotNull(message = "El ID del estudiante es obligatorio")
    private Long estudianteId;

    private String nombreEstudiante;

    @NotNull(message = "El ID de la materia es obligatorio")
    private Long materiaId;

    private String nombreMateria;
    private LocalDateTime fechaInscripcion;

    @Pattern(regexp = "^(PENDIENTE|APROBADA|RECHAZADA|CURSANDO|FINALIZADA)$",
             message = "El estado debe ser: PENDIENTE, APROBADA, RECHAZADA, CURSANDO o FINALIZADA")
    private String estado;

    @DecimalMin(value = "0.0", message = "La calificación no puede ser negativa")
    @DecimalMax(value = "10.0", message = "La calificación no puede ser mayor a 10")
    private Double calificacion;
}
