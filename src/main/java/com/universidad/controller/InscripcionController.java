package com.universidad.controller;

import com.universidad.dto.InscripcionDTO;
import com.universidad.service.IInscripcionService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.responses.ApiResponses;
import io.swagger.v3.oas.annotations.tags.Tag;

import java.util.List;

@RestController
@RequestMapping("/api/inscripciones")
@Tag(name = "Inscripciones", description = "API para gestionar las inscripciones de estudiantes a materias")
public class InscripcionController {

    private final IInscripcionService inscripcionService;
    private static final Logger logger = LoggerFactory.getLogger(InscripcionController.class);

    @Autowired
    public InscripcionController(IInscripcionService inscripcionService) {
        this.inscripcionService = inscripcionService;
    }

    @Operation(summary = "Obtener todas las inscripciones", description = "Retorna una lista de todas las inscripciones registradas")
    @ApiResponses(value = {
        @ApiResponse(responseCode = "200", description = "Inscripciones encontradas",
                     content = @Content(mediaType = "application/json",
                     schema = @Schema(implementation = InscripcionDTO.class)))
    })
    @GetMapping
    public ResponseEntity<List<InscripcionDTO>> obtenerTodasLasInscripciones() {
        long inicio = System.currentTimeMillis();
        logger.info("[INSCRIPCION] Inicio obtenerTodasLasInscripciones: {}", inicio);
        List<InscripcionDTO> inscripciones = inscripcionService.obtenerTodasLasInscripciones();
        long fin = System.currentTimeMillis();
        logger.info("[INSCRIPCION] Fin obtenerTodasLasInscripciones: {} (Duracion: {} ms)", fin, (fin-inicio));
        return ResponseEntity.ok(inscripciones);
    }

    @GetMapping("/{id}")
    public ResponseEntity<InscripcionDTO> obtenerInscripcionPorId(@PathVariable Long id) {
        long inicio = System.currentTimeMillis();
        logger.info("[INSCRIPCION] Inicio obtenerInscripcionPorId: {}", inicio);
        InscripcionDTO inscripcion = inscripcionService.obtenerInscripcionPorId(id);
        long fin = System.currentTimeMillis();
        logger.info("[INSCRIPCION] Fin obtenerInscripcionPorId: {} (Duracion: {} ms)", fin, (fin-inicio));

        if (inscripcion == null) {
            return ResponseEntity.notFound().build();
        }

        return ResponseEntity.ok(inscripcion);
    }

    @GetMapping("/estudiante/{estudianteId}")
    public ResponseEntity<List<InscripcionDTO>> obtenerInscripcionesPorEstudiante(@PathVariable Long estudianteId) {
        long inicio = System.currentTimeMillis();
        logger.info("[INSCRIPCION] Inicio obtenerInscripcionesPorEstudiante: {}", inicio);
        List<InscripcionDTO> inscripciones = inscripcionService.obtenerInscripcionesPorEstudiante(estudianteId);
        long fin = System.currentTimeMillis();
        logger.info("[INSCRIPCION] Fin obtenerInscripcionesPorEstudiante: {} (Duracion: {} ms)", fin, (fin-inicio));
        return ResponseEntity.ok(inscripciones);
    }

    @GetMapping("/materia/{materiaId}")
    public ResponseEntity<List<InscripcionDTO>> obtenerInscripcionesPorMateria(@PathVariable Long materiaId) {
        long inicio = System.currentTimeMillis();
        logger.info("[INSCRIPCION] Inicio obtenerInscripcionesPorMateria: {}", inicio);
        List<InscripcionDTO> inscripciones = inscripcionService.obtenerInscripcionesPorMateria(materiaId);
        long fin = System.currentTimeMillis();
        logger.info("[INSCRIPCION] Fin obtenerInscripcionesPorMateria: {} (Duracion: {} ms)", fin, (fin-inicio));
        return ResponseEntity.ok(inscripciones);
    }

    @Operation(summary = "Crear una nueva inscripción", description = "Crea una nueva inscripción de un estudiante a una materia")
    @ApiResponses(value = {
        @ApiResponse(responseCode = "201", description = "Inscripción creada exitosamente",
                     content = @Content(mediaType = "application/json",
                     schema = @Schema(implementation = InscripcionDTO.class))),
        @ApiResponse(responseCode = "400", description = "Datos inválidos o el estudiante ya está inscrito en la materia",
                     content = @Content)
    })
    @PostMapping
    public ResponseEntity<InscripcionDTO> crearInscripcion(
            @Parameter(description = "Datos de la inscripción a crear", required = true)
            @RequestBody InscripcionDTO inscripcionDTO) {
        try {
            long inicio = System.currentTimeMillis();
            logger.info("[INSCRIPCION] Inicio crearInscripcion: {}", inicio);
            InscripcionDTO nuevaInscripcion = inscripcionService.crearInscripcion(inscripcionDTO);
            long fin = System.currentTimeMillis();
            logger.info("[INSCRIPCION] Fin crearInscripcion: {} (Duracion: {} ms)", fin, (fin-inicio));
            return ResponseEntity.status(HttpStatus.CREATED).body(nuevaInscripcion);
        } catch (IllegalArgumentException e) {
            return ResponseEntity.badRequest().build();
        }
    }

    @PutMapping("/{id}")
    public ResponseEntity<InscripcionDTO> actualizarInscripcion(@PathVariable Long id, @RequestBody InscripcionDTO inscripcionDTO) {
        try {
            long inicio = System.currentTimeMillis();
            logger.info("[INSCRIPCION] Inicio actualizarInscripcion: {}", inicio);
            InscripcionDTO inscripcionActualizada = inscripcionService.actualizarInscripcion(id, inscripcionDTO);
            long fin = System.currentTimeMillis();
            logger.info("[INSCRIPCION] Fin actualizarInscripcion: {} (Duracion: {} ms)", fin, (fin-inicio));
            return ResponseEntity.ok(inscripcionActualizada);
        } catch (IllegalArgumentException e) {
            return ResponseEntity.notFound().build();
        }
    }

    @PatchMapping("/{id}/calificacion/{calificacion}")
    public ResponseEntity<InscripcionDTO> actualizarCalificacion(@PathVariable Long id, @PathVariable Double calificacion) {
        try {
            long inicio = System.currentTimeMillis();
            logger.info("[INSCRIPCION] Inicio actualizarCalificacion: {}", inicio);
            InscripcionDTO inscripcionActualizada = inscripcionService.actualizarCalificacion(id, calificacion);
            long fin = System.currentTimeMillis();
            logger.info("[INSCRIPCION] Fin actualizarCalificacion: {} (Duracion: {} ms)", fin, (fin-inicio));
            return ResponseEntity.ok(inscripcionActualizada);
        } catch (IllegalArgumentException e) {
            return ResponseEntity.notFound().build();
        }
    }

    @PatchMapping("/{id}/estado/{estado}")
    public ResponseEntity<InscripcionDTO> actualizarEstado(@PathVariable Long id, @PathVariable String estado) {
        try {
            long inicio = System.currentTimeMillis();
            logger.info("[INSCRIPCION] Inicio actualizarEstado: {}", inicio);
            InscripcionDTO inscripcionActualizada = inscripcionService.actualizarEstado(id, estado);
            long fin = System.currentTimeMillis();
            logger.info("[INSCRIPCION] Fin actualizarEstado: {} (Duracion: {} ms)", fin, (fin-inicio));
            return ResponseEntity.ok(inscripcionActualizada);
        } catch (IllegalArgumentException e) {
            return ResponseEntity.notFound().build();
        }
    }

    @DeleteMapping("/{id}")
    public ResponseEntity<Void> eliminarInscripcion(@PathVariable Long id) {
        try {
            long inicio = System.currentTimeMillis();
            logger.info("[INSCRIPCION] Inicio eliminarInscripcion: {}", inicio);
            inscripcionService.eliminarInscripcion(id);
            long fin = System.currentTimeMillis();
            logger.info("[INSCRIPCION] Fin eliminarInscripcion: {} (Duracion: {} ms)", fin, (fin-inicio));
            return ResponseEntity.noContent().build();
        } catch (IllegalArgumentException e) {
            return ResponseEntity.notFound().build();
        }
    }

    @Operation(summary = "Verificar prerrequisitos", description = "Verifica si un estudiante cumple con los prerrequisitos para inscribirse a una materia")
    @ApiResponses(value = {
        @ApiResponse(responseCode = "200", description = "Verificación realizada",
                     content = @Content(mediaType = "application/json",
                     schema = @Schema(implementation = Boolean.class)))
    })
    @GetMapping("/verificar-prerequisitos/{estudianteId}/{materiaId}")
    public ResponseEntity<Boolean> verificarPrerequisitos(
            @Parameter(description = "ID del estudiante", required = true) @PathVariable Long estudianteId,
            @Parameter(description = "ID de la materia", required = true) @PathVariable Long materiaId) {
        long inicio = System.currentTimeMillis();
        logger.info("[INSCRIPCION] Inicio verificarPrerequisitos: {}", inicio);
        boolean cumplePrerequisitos = inscripcionService.verificarPrerequisitos(estudianteId, materiaId);
        long fin = System.currentTimeMillis();
        logger.info("[INSCRIPCION] Fin verificarPrerequisitos: {} (Duracion: {} ms)", fin, (fin-inicio));
        return ResponseEntity.ok(cumplePrerequisitos);
    }

    @GetMapping("/verificar-cupo/{materiaId}")
    public ResponseEntity<Boolean> verificarCupoDisponible(@PathVariable Long materiaId) {
        long inicio = System.currentTimeMillis();
        logger.info("[INSCRIPCION] Inicio verificarCupoDisponible: {}", inicio);
        boolean hayCupo = inscripcionService.verificarCupoDisponible(materiaId);
        long fin = System.currentTimeMillis();
        logger.info("[INSCRIPCION] Fin verificarCupoDisponible: {} (Duracion: {} ms)", fin, (fin-inicio));
        return ResponseEntity.ok(hayCupo);
    }
}
