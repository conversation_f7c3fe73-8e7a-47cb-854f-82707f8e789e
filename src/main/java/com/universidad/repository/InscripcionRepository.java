package com.universidad.repository;

import com.universidad.model.Inscripcion;
import com.universidad.model.Estudiante;
import com.universidad.model.Materia;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Lock;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;
import jakarta.persistence.LockModeType;

import java.util.List;
import java.util.Optional;

@Repository
public interface InscripcionRepository extends JpaRepository<Inscripcion, Long> {

    List<Inscripcion> findByEstudiante(Estudiante estudiante);

    List<Inscripcion> findByMateria(Materia materia);

    Optional<Inscripcion> findByEstudianteAndMateria(Estudiante estudiante, Materia materia);

    // Eliminamos el bloqueo pesimista para resolver el problema de transacciones de solo lectura
    // @Lock(LockModeType.PESSIMISTIC_WRITE)
    // Optional<Inscripcion> findById(Long id);

    @Query("SELECT COUNT(i) FROM Inscripcion i WHERE i.materia.id = :materiaId AND i.estado = 'APROBADA'")
    Long countInscripcionesPorMateria(Long materiaId);
}
