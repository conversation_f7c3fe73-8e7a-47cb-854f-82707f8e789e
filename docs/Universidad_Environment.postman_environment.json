{"id": "universidad-environment", "name": "Universidad API Environment", "values": [{"key": "baseUrl", "value": "http://localhost:8080", "enabled": true}, {"key": "token", "value": "", "enabled": true}, {"key": "token_estudiante", "value": "", "enabled": true}, {"key": "token_docente", "value": "", "enabled": true}, {"key": "estudiante_id", "value": "1", "enabled": true}, {"key": "materia_id", "value": "1", "enabled": true}, {"key": "inscripcion_id", "value": "1", "enabled": true}], "_postman_variable_scope": "environment"}