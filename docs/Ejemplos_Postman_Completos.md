# Guía Completa de Pruebas con Postman - Sistema de Registro Universitario

Esta guía proporciona ejemplos paso a paso para probar toda la funcionalidad de tu API usando Postman, cumpliendo con todos los requisitos de la práctica.

## 📋 Configuración Inicial

### Variables de Entorno en Postman
Crea estas variables en Postman para facilitar las pruebas:
- `baseUrl`: `http://localhost:8080`
- `token`: (se actualizará automáticamente después del login)

## 🔐 1. AUTENTICACIÓN Y JWT

### 1.1 Registrar Usuario Administrador
**Método:** POST
**URL:** `{{baseUrl}}/api/auth/signup`
**Headers:**
```
Content-Type: application/json
```
**Body (JSON):**
```json
{
    "username": "admin_test",
    "email": "<EMAIL>",
    "password": "admin123456",
    "nombre": "Administrador",
    "apellido": "<PERSON><PERSON><PERSON>",
    "roles": ["admin"]
}
```

### 1.2 Registrar Usuario Docente
**Método:** POST
**URL:** `{{baseUrl}}/api/auth/signup`
**Body (JSON):**
```json
{
    "username": "docente_test",
    "email": "<EMAIL>",
    "password": "docente123456",
    "nombre": "María",
    "apellido": "García",
    "roles": ["docente"]
}
```

### 1.3 Registrar Usuario Estudiante
**Método:** POST
**URL:** `{{baseUrl}}/api/auth/signup`
**Body (JSON):**
```json
{
    "username": "estudiante_test",
    "email": "<EMAIL>",
    "password": "estudiante123456",
    "nombre": "Juan",
    "apellido": "Pérez",
    "roles": ["estudiante"]
}
```

### 1.4 Login (Obtener Token JWT)
**Método:** POST
**URL:** `{{baseUrl}}/api/auth/login`
**Body (JSON):**
```json
{
    "username": "admin_test",
    "password": "admin123456"
}
```

**Script Post-response (Tests):**
```javascript
if (pm.response.code === 200) {
    var jsonData = pm.response.json();
    pm.environment.set("token", jsonData.token);
    console.log("Token guardado: " + jsonData.token);
}
```

### 1.5 Verificar Información de Sesión
**Método:** GET
**URL:** `{{baseUrl}}/api/auth/session-info`
**Headers:**
```
Authorization: Bearer {{token}}
```

## 👨‍🎓 2. CRUD DE ESTUDIANTES

### 2.1 Crear Estudiante
**Método:** POST
**URL:** `{{baseUrl}}/api/estudiantes`
**Headers:**
```
Authorization: Bearer {{token}}
Content-Type: application/json
```
**Body (JSON):**
```json
{
    "nombre": "Carlos",
    "apellido": "Rodríguez",
    "email": "<EMAIL>",
    "fechaNacimiento": "2000-05-15",
    "numeroInscripcion": "EST001",
    "estado": "activo",
    "usuarioAlta": "admin_test",
    "fechaAlta": "2024-01-15"
}
```

### 2.2 Crear Segundo Estudiante
**Método:** POST
**URL:** `{{baseUrl}}/api/estudiantes`
**Body (JSON):**
```json
{
    "nombre": "Ana",
    "apellido": "López",
    "email": "<EMAIL>",
    "fechaNacimiento": "1999-08-22",
    "numeroInscripcion": "EST002",
    "estado": "activo",
    "usuarioAlta": "admin_test",
    "fechaAlta": "2024-01-15"
}
```

### 2.3 Obtener Todos los Estudiantes
**Método:** GET
**URL:** `{{baseUrl}}/api/estudiantes`
**Headers:**
```
Authorization: Bearer {{token}}
```

### 2.4 Obtener Estudiante por Número de Inscripción
**Método:** GET
**URL:** `{{baseUrl}}/api/estudiantes/inscripcion/EST001`
**Headers:**
```
Authorization: Bearer {{token}}
```

### 2.5 Actualizar Estudiante
**Método:** PUT
**URL:** `{{baseUrl}}/api/estudiantes/1`
**Headers:**
```
Authorization: Bearer {{token}}
Content-Type: application/json
```
**Body (JSON):**
```json
{
    "nombre": "Carlos Alberto",
    "apellido": "Rodríguez",
    "email": "<EMAIL>",
    "fechaNacimiento": "2000-05-15",
    "numeroInscripcion": "EST001",
    "estado": "activo",
    "usuarioAlta": "admin_test",
    "fechaAlta": "2024-01-15"
}
```

## 📚 3. CRUD DE MATERIAS Y ASIGNACIÓN A DOCENTES

### 3.1 Crear Primera Materia
**Método:** POST
**URL:** `{{baseUrl}}/api/materias`
**Headers:**
```
Authorization: Bearer {{token}}
Content-Type: application/json
```
**Body (JSON):**
```json
{
    "nombreMateria": "Programación I",
    "codigoUnico": "PROG001",
    "creditos": 4,
    "cupoMaximo": 30
}
```

### 3.2 Crear Segunda Materia
**Método:** POST
**URL:** `{{baseUrl}}/api/materias`
**Body (JSON):**
```json
{
    "nombreMateria": "Matemáticas Discretas",
    "codigoUnico": "MATH001",
    "creditos": 3,
    "cupoMaximo": 25
}
```

### 3.3 Crear Tercera Materia (con Prerrequisito)
**Método:** POST
**URL:** `{{baseUrl}}/api/materias`
**Body (JSON):**
```json
{
    "nombreMateria": "Programación II",
    "codigoUnico": "PROG002",
    "creditos": 4,
    "cupoMaximo": 25
}
```

### 3.4 Obtener Todas las Materias
**Método:** GET
**URL:** `{{baseUrl}}/api/materias`
**Headers:**
```
Authorization: Bearer {{token}}
```

### 3.5 Obtener Materia por ID
**Método:** GET
**URL:** `{{baseUrl}}/api/materias/1`
**Headers:**
```
Authorization: Bearer {{token}}
```

### 3.6 Obtener Materia por Código Único
**Método:** GET
**URL:** `{{baseUrl}}/api/materias/codigo/PROG001`
**Headers:**
```
Authorization: Bearer {{token}}
```

### 3.7 Actualizar Materia
**Método:** PUT
**URL:** `{{baseUrl}}/api/materias/1`
**Headers:**
```
Authorization: Bearer {{token}}
Content-Type: application/json
```
**Body (JSON):**
```json
{
    "nombreMateria": "Programación I - Fundamentos",
    "codigoUnico": "PROG001",
    "creditos": 4,
    "cupoMaximo": 35
}
```

## 📝 4. CRUD DE INSCRIPCIONES

### 4.1 Crear Primera Inscripción
**Método:** POST
**URL:** `{{baseUrl}}/api/inscripciones`
**Headers:**
```
Authorization: Bearer {{token}}
Content-Type: application/json
```
**Body (JSON):**
```json
{
    "estudianteId": 1,
    "materiaId": 1,
    "estado": "PENDIENTE"
}
```

### 4.2 Crear Segunda Inscripción
**Método:** POST
**URL:** `{{baseUrl}}/api/inscripciones`
**Body (JSON):**
```json
{
    "estudianteId": 2,
    "materiaId": 1,
    "estado": "APROBADA"
}
```

### 4.3 Crear Tercera Inscripción
**Método:** POST
**URL:** `{{baseUrl}}/api/inscripciones`
**Body (JSON):**
```json
{
    "estudianteId": 1,
    "materiaId": 2,
    "estado": "CURSANDO"
}
```

### 4.4 Obtener Todas las Inscripciones
**Método:** GET
**URL:** `{{baseUrl}}/api/inscripciones`
**Headers:**
```
Authorization: Bearer {{token}}
```

### 4.5 Obtener Inscripciones por Estudiante
**Método:** GET
**URL:** `{{baseUrl}}/api/inscripciones/estudiante/1`
**Headers:**
```
Authorization: Bearer {{token}}
```

### 4.6 Obtener Inscripciones por Materia
**Método:** GET
**URL:** `{{baseUrl}}/api/inscripciones/materia/1`
**Headers:**
```
Authorization: Bearer {{token}}
```

### 4.7 Actualizar Estado de Inscripción
**Método:** PATCH
**URL:** `{{baseUrl}}/api/inscripciones/1/estado/APROBADA`
**Headers:**
```
Authorization: Bearer {{token}}
```

### 4.8 Actualizar Calificación
**Método:** PATCH
**URL:** `{{baseUrl}}/api/inscripciones/1/calificacion/8.5`
**Headers:**
```
Authorization: Bearer {{token}}
```

### 4.9 Verificar Prerrequisitos
**Método:** GET
**URL:** `{{baseUrl}}/api/inscripciones/verificar-prerequisitos/1/3`
**Headers:**
```
Authorization: Bearer {{token}}
```

### 4.10 Verificar Cupo Disponible
**Método:** GET
**URL:** `{{baseUrl}}/api/inscripciones/verificar-cupo/1`
**Headers:**
```
Authorization: Bearer {{token}}
```

## 🧪 5. PRUEBAS DE VALIDACIONES

### 5.1 Probar Validación de Email Inválido (Estudiante)
**Método:** POST
**URL:** `{{baseUrl}}/api/estudiantes`
**Headers:**
```
Authorization: Bearer {{token}}
Content-Type: application/json
```
**Body (JSON):**
```json
{
    "nombre": "Test",
    "apellido": "Validación",
    "email": "email-invalido",
    "fechaNacimiento": "2000-01-01",
    "numeroInscripcion": "EST999",
    "estado": "activo",
    "usuarioAlta": "admin_test",
    "fechaAlta": "2024-01-15"
}
```
**Resultado Esperado:** Error 400 con mensaje de validación

### 5.2 Probar Validación de Campos Obligatorios (Materia)
**Método:** POST
**URL:** `{{baseUrl}}/api/materias`
**Headers:**
```
Authorization: Bearer {{token}}
Content-Type: application/json
```
**Body (JSON):**
```json
{
    "nombreMateria": "",
    "codigoUnico": "",
    "creditos": null
}
```
**Resultado Esperado:** Error 400 con múltiples mensajes de validación

### 5.3 Probar Validación de Calificación Inválida (Inscripción)
**Método:** POST
**URL:** `{{baseUrl}}/api/inscripciones`
**Headers:**
```
Authorization: Bearer {{token}}
Content-Type: application/json
```
**Body (JSON):**
```json
{
    "estudianteId": 1,
    "materiaId": 1,
    "estado": "APROBADA",
    "calificacion": 15.0
}
```
**Resultado Esperado:** Error 400 - calificación mayor a 10

### 5.4 Probar Email Duplicado
**Método:** POST
**URL:** `{{baseUrl}}/api/estudiantes`
**Headers:**
```
Authorization: Bearer {{token}}
Content-Type: application/json
```
**Body (JSON):**
```json
{
    "nombre": "Duplicado",
    "apellido": "Test",
    "email": "<EMAIL>",
    "fechaNacimiento": "2000-01-01",
    "numeroInscripcion": "EST888",
    "estado": "activo",
    "usuarioAlta": "admin_test",
    "fechaAlta": "2024-01-15"
}
```
**Resultado Esperado:** Error 409 - email ya existe

## 🔒 6. PRUEBAS DE AUTORIZACIÓN Y ROLES

### 6.1 Acceso Público (Sin Token)
**Método:** GET
**URL:** `{{baseUrl}}/api/public/test`
**Resultado Esperado:** 200 OK - "Contenido público"

### 6.2 Acceso Protegido Sin Token
**Método:** GET
**URL:** `{{baseUrl}}/api/estudiantes`
**Resultado Esperado:** 401 Unauthorized

### 6.3 Login como Estudiante
**Método:** POST
**URL:** `{{baseUrl}}/api/auth/login`
**Body (JSON):**
```json
{
    "username": "estudiante_test",
    "password": "estudiante123456"
}
```

### 6.4 Intentar Crear Estudiante con Rol Estudiante
**Método:** POST
**URL:** `{{baseUrl}}/api/estudiantes`
**Headers:**
```
Authorization: Bearer {{token_estudiante}}
Content-Type: application/json
```
**Body (JSON):**
```json
{
    "nombre": "No",
    "apellido": "Permitido",
    "email": "<EMAIL>",
    "fechaNacimiento": "2000-01-01",
    "numeroInscripcion": "EST777",
    "estado": "activo",
    "usuarioAlta": "estudiante_test",
    "fechaAlta": "2024-01-15"
}
```
**Resultado Esperado:** 403 Forbidden (solo ADMIN puede crear estudiantes)

### 6.5 Acceso de Estudiante a Contenido Permitido
**Método:** GET
**URL:** `{{baseUrl}}/api/estudiantes/test`
**Headers:**
```
Authorization: Bearer {{token_estudiante}}
```
**Resultado Esperado:** 200 OK - "Contenido para estudiantes"

## 🚀 7. PRUEBAS DE CACHÉ CON REDIS

### 7.1 Primera Consulta (Sin Caché)
**Método:** GET
**URL:** `{{baseUrl}}/api/estudiantes`
**Headers:**
```
Authorization: Bearer {{token}}
```
**Observar:** Tiempo de respuesta en logs del servidor

### 7.2 Segunda Consulta (Con Caché)
**Método:** GET
**URL:** `{{baseUrl}}/api/estudiantes`
**Headers:**
```
Authorization: Bearer {{token}}
```
**Observar:** Tiempo de respuesta menor (datos desde caché)

### 7.3 Invalidar Caché Creando Nuevo Estudiante
**Método:** POST
**URL:** `{{baseUrl}}/api/estudiantes`
**Headers:**
```
Authorization: Bearer {{token}}
Content-Type: application/json
```
**Body (JSON):**
```json
{
    "nombre": "Cache",
    "apellido": "Test",
    "email": "<EMAIL>",
    "fechaNacimiento": "2000-01-01",
    "numeroInscripcion": "EST555",
    "estado": "activo",
    "usuarioAlta": "admin_test",
    "fechaAlta": "2024-01-15"
}
```

### 7.4 Verificar Invalidación de Caché
**Método:** GET
**URL:** `{{baseUrl}}/api/estudiantes`
**Headers:**
```
Authorization: Bearer {{token}}
```
**Observar:** Tiempo de respuesta mayor (caché invalidado)

## 🗑️ 8. PRUEBAS DE ELIMINACIÓN (BAJA)

### 8.1 Eliminar Inscripción
**Método:** DELETE
**URL:** `{{baseUrl}}/api/inscripciones/1`
**Headers:**
```
Authorization: Bearer {{token}}
```
**Resultado Esperado:** 204 No Content

### 8.2 Eliminar Materia
**Método:** DELETE
**URL:** `{{baseUrl}}/api/materias/3`
**Headers:**
```
Authorization: Bearer {{token}}
```
**Resultado Esperado:** 204 No Content

### 8.3 Eliminar Estudiante
**Método:** DELETE
**URL:** `{{baseUrl}}/api/estudiantes/3`
**Headers:**
```
Authorization: Bearer {{token}}
```
**Resultado Esperado:** 204 No Content

## 📊 9. CASOS DE PRUEBA ESPECIALES

### 9.1 Inscripción Duplicada
**Método:** POST
**URL:** `{{baseUrl}}/api/inscripciones`
**Headers:**
```
Authorization: Bearer {{token}}
Content-Type: application/json
```
**Body (JSON):**
```json
{
    "estudianteId": 1,
    "materiaId": 1,
    "estado": "PENDIENTE"
}
```
**Resultado Esperado:** Error 400 - estudiante ya inscrito

### 9.2 Recurso No Encontrado
**Método:** GET
**URL:** `{{baseUrl}}/api/estudiantes/999`
**Headers:**
```
Authorization: Bearer {{token}}
```
**Resultado Esperado:** 404 Not Found

### 9.3 Endpoint No Existente
**Método:** GET
**URL:** `{{baseUrl}}/api/endpoint-inexistente`
**Headers:**
```
Authorization: Bearer {{token}}
```
**Resultado Esperado:** 404 Not Found con mensaje personalizado

## 📝 10. NOTAS PARA LA DOCUMENTACIÓN

### Capturas Recomendadas:
1. **Login exitoso** con token JWT
2. **Creación de estudiante** con validaciones
3. **Lista de materias** desde caché
4. **Error de validación** con múltiples campos
5. **Error de autorización** (403 Forbidden)
6. **Inscripción exitosa** con verificación de prerrequisitos
7. **Respuesta de Swagger** para cualquier endpoint

### Logs del Servidor a Observar:
- Tiempos de respuesta para demostrar caché
- Consultas SQL generadas por Hibernate
- Mensajes de validación y errores
- Información de autenticación JWT

### Variables de Postman Recomendadas:
```
baseUrl: http://localhost:8080
token: (se actualiza automáticamente)
token_estudiante: (para pruebas de roles)
token_docente: (para pruebas de roles)
```

¡Con estos ejemplos puedes probar completamente toda tu API y generar la documentación requerida para tu práctica! 🎉
