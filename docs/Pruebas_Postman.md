# Guía de Pruebas de la API con Postman

Este documento proporciona ejemplos detallados para probar cada funcionalidad de la API del Sistema de Registro Universitario utilizando Postman. Estas pruebas demuestran que la implementación cumple con todos los requisitos de la práctica.

## Tabla de Contenidos

1. [Autenticación y JWT](#1-autenticación-y-jwt)
2. [CRUD de Materias y Asignación a Docentes](#2-crud-de-materias-y-asignación-a-docentes)
3. [CRUD de Inscripciones por Estudiante](#3-crud-de-inscripciones-por-estudiante)
4. [Validaciones y Manejo de Excepciones](#4-validaciones-y-manejo-de-excepciones)
5. [<PERSON><PERSON> con Redis](#5-cache-con-redis)
6. [Documentación con Swagger](#6-documentación-con-swagger)

## 1. Autenticación y JWT

### Registro de Usuario
**Método:** POST  
**URL:** `http://localhost:8080/api/auth/signup`  
**Body (JSON):**
```json
{
  "username": "profesor1",
  "email": "<EMAIL>",
  "password": "password123",
  "nombre": "Juan",
  "apellido": "Pérez",
  "roles": ["docente"]
}
```
**Respuesta esperada:**
```json
{
  "message": "Usuario registrado exitosamente!"
}
```

### Inicio de Sesión
**Método:** POST  
**URL:** `http://localhost:8080/api/auth/login`  
**Body (JSON):**
```json
{
  "username": "profesor1",
  "password": "password123"
}
```
**Respuesta esperada:**
```json
{
  "token": "eyJhbGciOiJIUzUxMiJ9...",
  "type": "Bearer",
  "id": 2,
  "username": "profesor1",
  "email": "<EMAIL>",
  "roles": ["ROLE_DOCENTE"]
}
```
> **Importante:** Guarda este token para usarlo en las siguientes solicitudes.

### Verificación de Sesión
**Método:** GET  
**URL:** `http://localhost:8080/api/auth/session-info`  
**Headers:**
```
Authorization: Bearer eyJhbGciOiJIUzUxMiJ9...
```
**Respuesta esperada:**
```json
{
  "token": null,
  "type": "Bearer",
  "id": 2,
  "username": "profesor1",
  "email": "<EMAIL>",
  "roles": ["ROLE_DOCENTE"]
}
```

## 2. CRUD de Materias y Asignación a Docentes

### Crear Materia
**Método:** POST  
**URL:** `http://localhost:8080/api/materias`  
**Headers:**
```
Authorization: Bearer eyJhbGciOiJIUzUxMiJ9...
```
**Body (JSON):**
```json
{
  "nombreMateria": "Programación Avanzada",
  "codigoUnico": "PROG2023",
  "creditos": 4,
  "cupoMaximo": 30
}
```
**Respuesta esperada:**
```json
{
  "id": 1,
  "nombreMateria": "Programación Avanzada",
  "codigoUnico": "PROG2023",
  "creditos": 4,
  "cupoMaximo": 30,
  "docenteId": null,
  "nombreDocente": null,
  "prerequisitos": [],
  "esPrerequisitoDe": []
}
```

### Obtener Todas las Materias
**Método:** GET  
**URL:** `http://localhost:8080/api/materias`  
**Headers:**
```
Authorization: Bearer eyJhbGciOiJIUzUxMiJ9...
```
**Respuesta esperada:**
```json
[
  {
    "id": 1,
    "nombreMateria": "Programación Avanzada",
    "codigoUnico": "PROG2023",
    "creditos": 4,
    "cupoMaximo": 30,
    "docenteId": null,
    "nombreDocente": null,
    "prerequisitos": [],
    "esPrerequisitoDe": []
  }
]
```

### Asignar Docente a Materia
**Método:** POST  
**URL:** `http://localhost:8080/api/materias/1/asignar-docente/2`  
**Headers:**
```
Authorization: Bearer eyJhbGciOiJIUzUxMiJ9...
```
**Respuesta esperada:**
```json
{
  "id": 1,
  "nombreMateria": "Programación Avanzada",
  "codigoUnico": "PROG2023",
  "creditos": 4,
  "cupoMaximo": 30,
  "docenteId": 2,
  "nombreDocente": "Juan Pérez",
  "prerequisitos": [],
  "esPrerequisitoDe": []
}
```

### Obtener Materias por Docente
**Método:** GET  
**URL:** `http://localhost:8080/api/materias/por-docente/2`  
**Headers:**
```
Authorization: Bearer eyJhbGciOiJIUzUxMiJ9...
```
**Respuesta esperada:**
```json
[
  {
    "id": 1,
    "nombreMateria": "Programación Avanzada",
    "codigoUnico": "PROG2023",
    "creditos": 4,
    "cupoMaximo": 30,
    "docenteId": 2,
    "nombreDocente": "Juan Pérez",
    "prerequisitos": [],
    "esPrerequisitoDe": []
  }
]
```

### Actualizar Materia
**Método:** PUT  
**URL:** `http://localhost:8080/api/materias/1`  
**Headers:**
```
Authorization: Bearer eyJhbGciOiJIUzUxMiJ9...
```
**Body (JSON):**
```json
{
  "nombreMateria": "Programación Avanzada en Java",
  "codigoUnico": "PROG2023",
  "creditos": 5,
  "cupoMaximo": 25,
  "docenteId": 2
}
```
**Respuesta esperada:**
```json
{
  "id": 1,
  "nombreMateria": "Programación Avanzada en Java",
  "codigoUnico": "PROG2023",
  "creditos": 5,
  "cupoMaximo": 25,
  "docenteId": 2,
  "nombreDocente": "Juan Pérez",
  "prerequisitos": [],
  "esPrerequisitoDe": []
}
```

### Eliminar Materia
**Método:** DELETE  
**URL:** `http://localhost:8080/api/materias/1`  
**Headers:**
```
Authorization: Bearer eyJhbGciOiJIUzUxMiJ9...
```
**Respuesta esperada:**
- Código de estado: 204 No Content

## 3. CRUD de Inscripciones por Estudiante

### Registrar un Estudiante
**Método:** POST  
**URL:** `http://localhost:8080/api/auth/signup`  
**Body (JSON):**
```json
{
  "nombre": "María",
  "apellido": "González",
  "email": "<EMAIL>",
  "fechaNacimiento": "1998-05-15",
  "numeroInscripcion": "EST2023001",
  "estado": "activo",
  "usuarioAlta": "admin",
  "fechaAlta": "2025-05-19"
}
```
**Respuesta esperada:**
```json
{
  "message": "Usuario registrado exitosamente!"
}
```

### Crear Estudiante
**Método:** POST  
**URL:** `http://localhost:8080/api/estudiantes`  
**Headers:**
```
Authorization: Bearer eyJhbGciOiJIUzUxMiJ9...
```
**Body (JSON):**
```json
{
  "nombre": "María",
  "apellido": "González",
  "email": "<EMAIL>",
  "fechaNacimiento": "1998-05-15",
  "numeroInscripcion": "EST2023001",
  "estado": "activo"
}
```
**Respuesta esperada:**
```json
{
  "id": 1,
  "nombre": "María",
  "apellido": "González",
  "email": "<EMAIL>",
  "fechaNacimiento": "1998-05-15",
  "numeroInscripcion": "EST2023001",
  "estado": "activo"
}
```

### Crear Inscripción
**Método:** POST  
**URL:** `http://localhost:8080/api/inscripciones`  
**Headers:**
```
Authorization: Bearer eyJhbGciOiJIUzUxMiJ9...
```
**Body (JSON):**
```json
{
  "estudianteId": 1,
  "materiaId": 1,
  "estado": "PENDIENTE"
}
```
**Respuesta esperada:**
```json
{
  "id": 1,
  "estudianteId": 1,
  "nombreEstudiante": "María González",
  "materiaId": 1,
  "nombreMateria": "Programación Avanzada en Java",
  "fechaInscripcion": "2023-05-20T14:30:45.123",
  "estado": "PENDIENTE",
  "calificacion": null
}
```

### Obtener Inscripciones por Estudiante
**Método:** GET  
**URL:** `http://localhost:8080/api/inscripciones/estudiante/1`  
**Headers:**
```
Authorization: Bearer eyJhbGciOiJIUzUxMiJ9...
```
**Respuesta esperada:**
```json
[
  {
    "id": 1,
    "estudianteId": 1,
    "nombreEstudiante": "María González",
    "materiaId": 1,
    "nombreMateria": "Programación Avanzada en Java",
    "fechaInscripcion": "2023-05-20T14:30:45.123",
    "estado": "PENDIENTE",
    "calificacion": null
  }
]
```

### Actualizar Estado de Inscripción
**Método:** PATCH  
**URL:** `http://localhost:8080/api/inscripciones/1/estado/APROBADA`  
**Headers:**
```
Authorization: Bearer eyJhbGciOiJIUzUxMiJ9...
```
**Respuesta esperada:**
```json
{
  "id": 1,
  "estudianteId": 1,
  "nombreEstudiante": "María González",
  "materiaId": 1,
  "nombreMateria": "Programación Avanzada en Java",
  "fechaInscripcion": "2023-05-20T14:30:45.123",
  "estado": "APROBADA",
  "calificacion": null
}
```

### Asignar Calificación
**Método:** PATCH  
**URL:** `http://localhost:8080/api/inscripciones/1/calificacion/8.5`  
**Headers:**
```
Authorization: Bearer eyJhbGciOiJIUzUxMiJ9...
```
**Respuesta esperada:**
```json
{
  "id": 1,
  "estudianteId": 1,
  "nombreEstudiante": "María González",
  "materiaId": 1,
  "nombreMateria": "Programación Avanzada en Java",
  "fechaInscripcion": "2023-05-20T14:30:45.123",
  "estado": "APROBADA",
  "calificacion": 8.5
}
```

### Eliminar Inscripción
**Método:** DELETE  
**URL:** `http://localhost:8080/api/inscripciones/1`  
**Headers:**
```
Authorization: Bearer eyJhbGciOiJIUzUxMiJ9...
```
**Respuesta esperada:**
- Código de estado: 204 No Content

## 4. Validaciones y Manejo de Excepciones

### Intentar Crear Materia con Datos Inválidos
**Método:** POST  
**URL:** `http://localhost:8080/api/materias`  
**Headers:**
```
Authorization: Bearer eyJhbGciOiJIUzUxMiJ9...
```
**Body (JSON):**
```json
{
  "nombreMateria": "",
  "codigoUnico": "",
  "creditos": -1
}
```
**Respuesta esperada (400 Bad Request):**
```json
{
  "status": 400,
  "mensaje": "Error de validación en los datos de entrada",
  "detalles": {
    "nombreMateria": "El nombre de la materia no puede estar vacío",
    "codigoUnico": "El código único no puede estar vacío",
    "creditos": "Los créditos deben ser un valor positivo"
  },
  "timestamp": "2023-05-20T15:30:45.123"
}
```

### Intentar Inscribir a un Estudiante en una Materia que Ya Cursa
**Método:** POST  
**URL:** `http://localhost:8080/api/inscripciones`  
**Headers:**
```
Authorization: Bearer eyJhbGciOiJIUzUxMiJ9...
```
**Body (JSON):**
```json
{
  "estudianteId": 1,
  "materiaId": 1,
  "estado": "PENDIENTE"
}
```
**Respuesta esperada (400 Bad Request):**
```json
{
  "status": 400,
  "mensaje": "Error de validación en los datos de entrada",
  "detalles": "El estudiante ya está inscrito en esta materia",
  "timestamp": "2023-05-20T15:35:45.123"
}
```

### Intentar Acceder a un Recurso No Existente
**Método:** GET  
**URL:** `http://localhost:8080/api/materias/999`  
**Headers:**
```
Authorization: Bearer eyJhbGciOiJIUzUxMiJ9...
```
**Respuesta esperada (404 Not Found):**
```json
{
  "status": 404,
  "mensaje": "Recurso no encontrado",
  "detalles": "Materia no encontrada",
  "timestamp": "2023-05-20T15:40:45.123"
}
```

## 5. Cache con Redis

Para demostrar el funcionamiento del caché, puedes realizar la misma solicitud varias veces y observar los tiempos de respuesta en los logs del servidor.

### Primera Solicitud (sin caché)
**Método:** GET  
**URL:** `http://localhost:8080/api/materias`  
**Headers:**
```
Authorization: Bearer eyJhbGciOiJIUzUxMiJ9...
```

En los logs del servidor, deberías ver algo como:
```
[MATERIA] Inicio obtenerTodasLasMaterias: 1684598400000
[MATERIA] Fin obtenerTodasLasMaterias: 1684598400500 (Duracion: 500 ms)
```

### Segunda Solicitud (con caché)
**Método:** GET  
**URL:** `http://localhost:8080/api/materias`  
**Headers:**
```
Authorization: Bearer eyJhbGciOiJIUzUxMiJ9...
```

En los logs del servidor, deberías ver un tiempo de respuesta mucho menor:
```
[MATERIA] Inicio obtenerTodasLasMaterias: 1684598410000
[MATERIA] Fin obtenerTodasLasMaterias: 1684598410050 (Duracion: 50 ms)
```

### Invalidación de Caché
Para demostrar la invalidación del caché, puedes realizar una operación de escritura (POST, PUT, DELETE) y luego volver a realizar la solicitud GET. El tiempo de respuesta debería ser similar al de la primera solicitud, ya que el caché se habrá invalidado.

**Método:** POST  
**URL:** `http://localhost:8080/api/materias`  
**Headers:**
```
Authorization: Bearer eyJhbGciOiJIUzUxMiJ9...
```
**Body (JSON):**
```json
{
  "nombreMateria": "Nueva Materia",
  "codigoUnico": "NUEVA2023",
  "creditos": 3,
  "cupoMaximo": 20
}
```

Luego, realiza una solicitud GET a `/api/materias` y observa los logs. El tiempo de respuesta debería ser mayor que en la segunda solicitud.

## 6. Documentación con Swagger

Para verificar la documentación con Swagger, simplemente abre en tu navegador:
```
http://localhost:8080/swagger-ui.html
```

Deberías ver una interfaz interactiva que muestra todos los endpoints de tu API, organizados por controladores, con descripciones detalladas y la posibilidad de probarlos directamente desde la interfaz.

### Elementos a verificar en Swagger:

1. **Página principal**: Muestra todos los controladores disponibles (Autenticación, Estudiantes, Materias, Inscripciones).

2. **Documentación de controladores**: Al expandir cada controlador, se muestran todos sus endpoints con descripciones detalladas.

3. **Esquemas de datos**: Muestra los modelos de datos utilizados por la API (MateriaDTO, EstudianteDTO, InscripcionDTO, etc.).

4. **Prueba de endpoints**: Permite probar los endpoints directamente desde la interfaz, proporcionando formularios para ingresar los parámetros y cuerpos de solicitud.

5. **Respuestas documentadas**: Muestra los posibles códigos de respuesta para cada endpoint, con descripciones de lo que significan.

## Consejos para Documentar las Pruebas

1. **Capturas de pantalla**: Para cada punto importante, toma capturas de pantalla de Postman mostrando la solicitud y la respuesta.

2. **Organización**: Organiza las capturas de pantalla por funcionalidad (autenticación, CRUD de materias, etc.).

3. **Explicaciones**: Añade breves explicaciones de lo que demuestra cada prueba y cómo se relaciona con los requisitos de la práctica.

4. **Tiempos de respuesta**: Para demostrar el funcionamiento del caché, incluye capturas de pantalla de los logs mostrando la diferencia en los tiempos de respuesta.

5. **Errores**: Documenta cómo maneja tu API los errores, mostrando las respuestas de error personalizadas.

6. **Swagger**: Incluye capturas de pantalla de la interfaz de Swagger para demostrar la documentación de la API.

