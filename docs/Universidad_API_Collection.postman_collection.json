{"info": {"_postman_id": "universidad-api-collection", "name": "Universidad API - Práctica Completa", "description": "Colección completa para probar la API del Sistema de Registro Universitario", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json"}, "item": [{"name": "1. Autenticación", "item": [{"name": "Registrar Ad<PERSON>", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"username\": \"admin_test\",\n    \"email\": \"<EMAIL>\",\n    \"password\": \"admin123456\",\n    \"nombre\": \"Administrador\",\n    \"apellido\": \"<PERSON><PERSON><PERSON>\",\n    \"roles\": [\"admin\"]\n}"}, "url": {"raw": "{{baseUrl}}/api/auth/signup", "host": ["{{baseUrl}}"], "path": ["api", "auth", "signup"]}}}, {"name": "<PERSON><PERSON>", "event": [{"listen": "test", "script": {"exec": ["if (pm.response.code === 200) {", "    var jsonData = pm.response.json();", "    pm.environment.set(\"token\", jsonData.token);", "    console.log(\"Token guardado: \" + jsonData.token);", "}"], "type": "text/javascript"}}], "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"username\": \"admin_test\",\n    \"password\": \"admin123456\"\n}"}, "url": {"raw": "{{baseUrl}}/api/auth/login", "host": ["{{baseUrl}}"], "path": ["api", "auth", "login"]}}}, {"name": "Verificar Sesión", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{token}}"}], "url": {"raw": "{{baseUrl}}/api/auth/session-info", "host": ["{{baseUrl}}"], "path": ["api", "auth", "session-info"]}}}]}, {"name": "2. Est<PERSON><PERSON><PERSON>", "item": [{"name": "<PERSON><PERSON><PERSON>", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{token}}"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"nombre\": \"<PERSON>\",\n    \"apellido\": \"<PERSON>\",\n    \"email\": \"<EMAIL>\",\n    \"fechaNacimiento\": \"2000-05-15\",\n    \"numeroInscripcion\": \"EST001\",\n    \"estado\": \"activo\",\n    \"usuarioAlta\": \"admin_test\",\n    \"fechaAlta\": \"2024-01-15\"\n}"}, "url": {"raw": "{{baseUrl}}/api/estudiantes", "host": ["{{baseUrl}}"], "path": ["api", "estudiantes"]}}}, {"name": "Obtener Todos los Estudiantes", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{token}}"}], "url": {"raw": "{{baseUrl}}/api/estudiantes", "host": ["{{baseUrl}}"], "path": ["api", "estudiantes"]}}}, {"name": "Obtener Estudiante por Número", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{token}}"}], "url": {"raw": "{{baseUrl}}/api/estudiantes/inscripcion/EST001", "host": ["{{baseUrl}}"], "path": ["api", "estudiantes", "inscripcion", "EST001"]}}}, {"name": "Actualizar E<PERSON>udi<PERSON>", "request": {"method": "PUT", "header": [{"key": "Authorization", "value": "Bearer {{token}}"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"nombre\": \"<PERSON>\",\n    \"apellido\": \"<PERSON>\",\n    \"email\": \"<EMAIL>\",\n    \"fechaNacimiento\": \"2000-05-15\",\n    \"numeroInscripcion\": \"EST001\",\n    \"estado\": \"activo\",\n    \"usuarioAlta\": \"admin_test\",\n    \"fechaAlta\": \"2024-01-15\"\n}"}, "url": {"raw": "{{baseUrl}}/api/estudiantes/1", "host": ["{{baseUrl}}"], "path": ["api", "estudiantes", "1"]}}}]}, {"name": "3. <PERSON><PERSON><PERSON>", "item": [{"name": "<PERSON><PERSON><PERSON>", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{token}}"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"nombreMateria\": \"Programación I\",\n    \"codigoUnico\": \"PROG001\",\n    \"creditos\": 4,\n    \"cupoMaximo\": 30\n}"}, "url": {"raw": "{{baseUrl}}/api/materias", "host": ["{{baseUrl}}"], "path": ["api", "materias"]}}}, {"name": "Obtener Todas las Materias", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{token}}"}], "url": {"raw": "{{baseUrl}}/api/materias", "host": ["{{baseUrl}}"], "path": ["api", "materias"]}}}, {"name": "Obtener Materia por Código", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{token}}"}], "url": {"raw": "{{baseUrl}}/api/materias/codigo/PROG001", "host": ["{{baseUrl}}"], "path": ["api", "materias", "codigo", "PROG001"]}}}]}, {"name": "4. Inscripciones", "item": [{"name": "<PERSON><PERSON>r Inscrip<PERSON>", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{token}}"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"estudianteId\": 1,\n    \"materiaId\": 1,\n    \"estado\": \"PENDIENTE\"\n}"}, "url": {"raw": "{{baseUrl}}/api/inscripciones", "host": ["{{baseUrl}}"], "path": ["api", "inscripciones"]}}}, {"name": "Obtener Todas las Inscripciones", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{token}}"}], "url": {"raw": "{{baseUrl}}/api/inscripciones", "host": ["{{baseUrl}}"], "path": ["api", "inscripciones"]}}}, {"name": "Actualizar Calificación", "request": {"method": "PATCH", "header": [{"key": "Authorization", "value": "Bearer {{token}}"}], "url": {"raw": "{{baseUrl}}/api/inscripciones/1/calificacion/8.5", "host": ["{{baseUrl}}"], "path": ["api", "inscripciones", "1", "calificacion", "8.5"]}}}]}, {"name": "5. Validaciones", "item": [{"name": "<PERSON><PERSON>", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{token}}"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"nombre\": \"Test\",\n    \"apellido\": \"Validación\",\n    \"email\": \"email-invalido\",\n    \"fechaNacimiento\": \"2000-01-01\",\n    \"numeroInscripcion\": \"EST999\",\n    \"estado\": \"activo\",\n    \"usuarioAlta\": \"admin_test\",\n    \"fechaAlta\": \"2024-01-15\"\n}"}, "url": {"raw": "{{baseUrl}}/api/estudiantes", "host": ["{{baseUrl}}"], "path": ["api", "estudiantes"]}}}, {"name": "Campos Obligatorios Materia", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{token}}"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"nombreMateria\": \"\",\n    \"codigoUnico\": \"\",\n    \"creditos\": null\n}"}, "url": {"raw": "{{baseUrl}}/api/materias", "host": ["{{baseUrl}}"], "path": ["api", "materias"]}}}]}], "variable": [{"key": "baseUrl", "value": "http://localhost:8080"}]}