# 🚀 Guía Rápida - Pruebas con Postman

## 📥 Importar Archivos

1. **Importar Colección:**
   - <PERSON><PERSON> Postman
   - Click en "Import"
   - Selecciona `docs/Universidad_API_Collection.postman_collection.json`

2. **Importar Entorno:**
   - Click en "Import"
   - Selecciona `docs/Universidad_Environment.postman_environment.json`
   - Activa el entorno "Universidad API Environment"

## ⚡ Secuencia de Pruebas Básicas

### 1. Iniciar Aplicación
```bash
# Asegúrate de que PostgreSQL y Redis estén ejecutándose
mvn spring-boot:run
```

### 2. Autenticación (OBLIGATORIO PRIMERO)
1. **Registrar Admin:** `POST /api/auth/signup`
2. **Login Admin:** `POST /api/auth/login` (guarda el token automáticamente)
3. **Verificar Sesión:** `GET /api/auth/session-info`

### 3. Crear <PERSON> Base
1. **<PERSON><PERSON>r Estudiante:** `POST /api/estudiantes`
2. **<PERSON><PERSON>r <PERSON>:** `POST /api/materias`
3. **Crear Inscripción:** `POST /api/inscripciones`

### 4. Probar CRUD Completo
1. **Listar:** `GET /api/estudiantes`, `GET /api/materias`, `GET /api/inscripciones`
2. **Obtener por ID:** `GET /api/estudiantes/1`
3. **Actualizar:** `PUT /api/estudiantes/1`
4. **Eliminar:** `DELETE /api/estudiantes/1`

## 🧪 Pruebas Críticas para la Práctica

### ✅ Validaciones (Capturar Errores)
- **Email inválido:** Usar "email-invalido" en lugar de email válido
- **Campos vacíos:** Enviar campos obligatorios vacíos
- **Calificación inválida:** Usar calificación > 10

### ✅ JWT y Roles
- **Sin token:** Intentar acceder a `/api/estudiantes` sin Authorization header
- **Rol incorrecto:** Login como estudiante e intentar crear otro estudiante

### ✅ Cache con Redis
- **Primera consulta:** `GET /api/estudiantes` (observar tiempo en logs)
- **Segunda consulta:** `GET /api/estudiantes` (debería ser más rápida)
- **Invalidar cache:** `POST /api/estudiantes` (crear nuevo)
- **Verificar invalidación:** `GET /api/estudiantes` (tiempo mayor nuevamente)

### ✅ Manejo de Excepciones
- **Recurso no encontrado:** `GET /api/estudiantes/999`
- **Endpoint inexistente:** `GET /api/endpoint-falso`
- **Inscripción duplicada:** Crear la misma inscripción dos veces

## 📸 Capturas Recomendadas

1. **Login exitoso** mostrando el token JWT
2. **Creación de estudiante** con respuesta 201
3. **Error de validación** con múltiples campos (400)
4. **Error de autorización** sin token (401)
5. **Lista de materias** desde cache
6. **Swagger UI** con cualquier endpoint
7. **Logs del servidor** mostrando tiempos de cache

## 🔧 Variables Importantes

Asegúrate de que estas variables estén configuradas en tu entorno:
- `baseUrl`: `http://localhost:8080`
- `token`: Se actualiza automáticamente después del login
- `token_estudiante`: Para pruebas de roles específicos

## 📊 Resultados Esperados

| Prueba | Método | URL | Código Esperado | Observación |
|--------|--------|-----|-----------------|-------------|
| Login | POST | /api/auth/login | 200 | Token en respuesta |
| Crear Estudiante | POST | /api/estudiantes | 201 | Con validaciones |
| Email Inválido | POST | /api/estudiantes | 400 | Error de validación |
| Sin Token | GET | /api/estudiantes | 401 | No autorizado |
| Recurso No Existe | GET | /api/estudiantes/999 | 404 | No encontrado |
| Cache Primera Vez | GET | /api/estudiantes | 200 | Tiempo mayor |
| Cache Segunda Vez | GET | /api/estudiantes | 200 | Tiempo menor |

## 🎯 Tips para la Documentación

1. **Captura pantallas completas** incluyendo headers y respuestas
2. **Incluye los logs del servidor** para mostrar el cache funcionando
3. **Documenta tanto casos exitosos como errores**
4. **Muestra la diferencia de tiempos** en las consultas con cache
5. **Incluye ejemplos de cada tipo de validación**

## 🚨 Problemas Comunes

- **Error 401:** Verifica que el token esté en el header Authorization
- **Error 403:** Verifica que el usuario tenga el rol correcto
- **Error de conexión:** Asegúrate de que PostgreSQL y Redis estén ejecutándose
- **Token expirado:** Vuelve a hacer login para obtener un nuevo token

¡Con esta guía puedes probar completamente tu API y generar toda la documentación necesaria para tu práctica! 🎉
